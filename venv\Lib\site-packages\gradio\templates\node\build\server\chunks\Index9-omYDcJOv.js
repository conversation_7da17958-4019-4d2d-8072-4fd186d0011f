import { c as create_ssr_component, b as createEventDispatcher, v as validate_component } from './ssr-C3HYbsxA.js';
import { m as mt, b as zA, _ as _e } from './2-CUxBFVNo.js';
import wt from './Gallery-DTlEQf7R.js';
import { BaseFileUpload as lt } from './Index10-BIAbv6k3.js';
import './index-ClteBeTX.js';
import './Component-NmRBwSfF.js';
import 'tty';
import 'path';
import 'url';
import 'fs';
import './ModifyUpload-Ca-f84bx.js';
import './Video-Dpbo0gee.js';
import './hls-DpKhbIaL.js';
import './index8-DWEiEAhs.js';
import './Example12-QUrFCQ6n.js';

const Z=create_ssr_component((a,e,l,P)=>{let O,{loading_status:r}=e,{show_label:w}=e,{label:u}=e,{root:m}=e,{elem_id:x=""}=e,{elem_classes:y=[]}=e,{visible:s=!0}=e,{value:i=null}=e,{file_types:j=["image","video"]}=e,{container:B=!0}=e,{scale:U=null}=e,{min_width:k=void 0}=e,{columns:G=[2]}=e,{rows:S=void 0}=e,{height:v="auto"}=e,{preview:T}=e,{allow_preview:z=!0}=e,{selected_index:_=null}=e,{object_fit:F="cover"}=e,{show_share_button:D=!1}=e,{interactive:h}=e,{show_download_button:E=!1}=e,{gradio:t}=e,{show_fullscreen_button:I=!0}=e,{fullscreen:d=!1}=e;const q=createEventDispatcher();e.loading_status===void 0&&l.loading_status&&r!==void 0&&l.loading_status(r),e.show_label===void 0&&l.show_label&&w!==void 0&&l.show_label(w),e.label===void 0&&l.label&&u!==void 0&&l.label(u),e.root===void 0&&l.root&&m!==void 0&&l.root(m),e.elem_id===void 0&&l.elem_id&&x!==void 0&&l.elem_id(x),e.elem_classes===void 0&&l.elem_classes&&y!==void 0&&l.elem_classes(y),e.visible===void 0&&l.visible&&s!==void 0&&l.visible(s),e.value===void 0&&l.value&&i!==void 0&&l.value(i),e.file_types===void 0&&l.file_types&&j!==void 0&&l.file_types(j),e.container===void 0&&l.container&&B!==void 0&&l.container(B),e.scale===void 0&&l.scale&&U!==void 0&&l.scale(U),e.min_width===void 0&&l.min_width&&k!==void 0&&l.min_width(k),e.columns===void 0&&l.columns&&G!==void 0&&l.columns(G),e.rows===void 0&&l.rows&&S!==void 0&&l.rows(S),e.height===void 0&&l.height&&v!==void 0&&l.height(v),e.preview===void 0&&l.preview&&T!==void 0&&l.preview(T),e.allow_preview===void 0&&l.allow_preview&&z!==void 0&&l.allow_preview(z),e.selected_index===void 0&&l.selected_index&&_!==void 0&&l.selected_index(_),e.object_fit===void 0&&l.object_fit&&F!==void 0&&l.object_fit(F),e.show_share_button===void 0&&l.show_share_button&&D!==void 0&&l.show_share_button(D),e.interactive===void 0&&l.interactive&&h!==void 0&&l.interactive(h),e.show_download_button===void 0&&l.show_download_button&&E!==void 0&&l.show_download_button(E),e.gradio===void 0&&l.gradio&&t!==void 0&&l.gradio(t),e.show_fullscreen_button===void 0&&l.show_fullscreen_button&&I!==void 0&&l.show_fullscreen_button(I),e.fullscreen===void 0&&l.fullscreen&&d!==void 0&&l.fullscreen(d);let f,n,A=a.head;do f=!0,a.head=A,O=i===null?!0:i.length===0,q("prop_change",{selected_index:_}),n=`${validate_component(mt,"Block").$$render(a,{visible:s,variant:"solid",padding:!1,elem_id:x,elem_classes:y,container:B,scale:U,min_width:k,allow_overflow:!1,height:typeof v=="number"?v:void 0,fullscreen:d},{fullscreen:o=>{d=o,f=!1;}},{default:()=>`${validate_component(zA,"StatusTracker").$$render(a,Object.assign({},{autoscroll:t.autoscroll},{i18n:t.i18n},r),{},{})} ${h&&O?`${validate_component(lt,"BaseFileUpload").$$render(a,{value:null,root:m,label:u,max_file_size:t.max_file_size,file_count:"multiple",file_types:j,i18n:t.i18n,upload:(...o)=>t.client.upload(...o),stream_handler:(...o)=>t.client.stream(...o)},{},{default:()=>`${validate_component(_e,"UploadText").$$render(a,{i18n:t.i18n,type:"gallery"},{},{})}`})}`:`${validate_component(wt,"Gallery").$$render(a,{label:u,show_label:w,columns:G,rows:S,height:v,preview:T,object_fit:F,interactive:h,allow_preview:z,show_share_button:D,show_download_button:E,i18n:t.i18n,_fetch:(...o)=>t.client.fetch(...o),show_fullscreen_button:I,fullscreen:d,selected_index:_,value:i},{selected_index:o=>{_=o,f=!1;},value:o=>{i=o,f=!1;}},{})}`}`})}`;while(!f);return n});

export { wt as BaseGallery, Z as default };
//# sourceMappingURL=Index9-omYDcJOv.js.map
