import { t as tick } from './Component-NmRBwSfF.js';
import { c as create_ssr_component, b as createEventDispatcher, v as validate_component, d as add_attribute, h as add_styles, o as onDestroy, e as escape } from './ssr-C3HYbsxA.js';
import { i as Ke, n as j, V as Vt, y as pe, C, U as Ut, x as qt, ab as ta } from './2-CUxBFVNo.js';

const se={code:`.wrap.svelte-1vsfomn.svelte-1vsfomn{overflow-y:auto;transition:opacity 0.5s ease-in-out;background:var(--block-background-fill);position:relative;display:flex;flex-direction:column;align-items:center;justify-content:center;min-height:var(--size-40);width:var(--size-full)}.wrap.svelte-1vsfomn.svelte-1vsfomn::after{content:"";position:absolute;top:0;left:0;width:var(--upload-progress-width);height:100%;transition:all 0.5s ease-in-out;z-index:1}.uploading.svelte-1vsfomn.svelte-1vsfomn{font-size:var(--text-lg);font-family:var(--font);z-index:2}.file-name.svelte-1vsfomn.svelte-1vsfomn{margin:var(--spacing-md);font-size:var(--text-lg);color:var(--body-text-color-subdued)}.file.svelte-1vsfomn.svelte-1vsfomn{font-size:var(--text-md);z-index:2;display:flex;align-items:center}.file.svelte-1vsfomn progress.svelte-1vsfomn{display:inline;height:var(--size-1);width:100%;transition:all 0.5s ease-in-out;color:var(--color-accent);border:none}.file.svelte-1vsfomn progress[value].svelte-1vsfomn::-webkit-progress-value{background-color:var(--color-accent);border-radius:20px}.file.svelte-1vsfomn progress[value].svelte-1vsfomn::-webkit-progress-bar{background-color:var(--border-color-accent);border-radius:20px}.progress-bar.svelte-1vsfomn.svelte-1vsfomn{width:14px;height:14px;border-radius:50%;background:radial-gradient(
				closest-side,
				var(--block-background-fill) 64%,
				transparent 53% 100%
			),
			conic-gradient(
				var(--color-accent) var(--upload-progress-width),
				var(--border-color-accent) 0
			);transition:all 0.5s ease-in-out}`,map:'{"version":3,"file":"UploadProgress.svelte","sources":["UploadProgress.svelte"],"sourcesContent":["<script lang=\\"ts\\">import { FileData } from \\"@gradio/client\\";\\nimport { onMount, createEventDispatcher, onDestroy } from \\"svelte\\";\\nexport let upload_id;\\nexport let root;\\nexport let files;\\nexport let stream_handler;\\nlet stream;\\nlet progress = false;\\nlet current_file_upload;\\nlet file_to_display;\\nlet files_with_progress = files.map((file) => {\\n    return {\\n        ...file,\\n        progress: 0\\n    };\\n});\\nconst dispatch = createEventDispatcher();\\nfunction handleProgress(filename, chunk_size) {\\n    files_with_progress = files_with_progress.map((file) => {\\n        if (file.orig_name === filename) {\\n            file.progress += chunk_size;\\n        }\\n        return file;\\n    });\\n}\\nfunction getProgress(file) {\\n    return file.progress * 100 / (file.size || 0) || 0;\\n}\\nonMount(async () => {\\n    stream = await stream_handler(new URL(`${root}/gradio_api/upload_progress?upload_id=${upload_id}`));\\n    if (stream == null) {\\n        throw new Error(\\"Event source is not defined\\");\\n    }\\n    stream.onmessage = async function (event) {\\n        const _data = JSON.parse(event.data);\\n        if (!progress)\\n            progress = true;\\n        if (_data.msg === \\"done\\") {\\n            stream?.close();\\n            dispatch(\\"done\\");\\n        }\\n        else {\\n            current_file_upload = _data;\\n            handleProgress(_data.orig_name, _data.chunk_size);\\n        }\\n    };\\n});\\nonDestroy(() => {\\n    if (stream != null || stream != void 0)\\n        stream.close();\\n});\\nfunction calculateTotalProgress(files2) {\\n    let totalProgress = 0;\\n    files2.forEach((file) => {\\n        totalProgress += getProgress(file);\\n    });\\n    document.documentElement.style.setProperty(\\"--upload-progress-width\\", (totalProgress / files2.length).toFixed(2) + \\"%\\");\\n    return totalProgress / files2.length;\\n}\\n$: calculateTotalProgress(files_with_progress);\\n$: file_to_display = current_file_upload || files_with_progress[0];\\n<\/script>\\n\\n<div class=\\"wrap\\" class:progress>\\n\\t<span class=\\"uploading\\"\\n\\t\\t>Uploading {files_with_progress.length}\\n\\t\\t{files_with_progress.length > 1 ? \\"files\\" : \\"file\\"}...</span\\n\\t>\\n\\n\\t{#if file_to_display}\\n\\t\\t<div class=\\"file\\">\\n\\t\\t\\t<span>\\n\\t\\t\\t\\t<div class=\\"progress-bar\\">\\n\\t\\t\\t\\t\\t<progress\\n\\t\\t\\t\\t\\t\\tstyle=\\"visibility:hidden;height:0;width:0;\\"\\n\\t\\t\\t\\t\\t\\tvalue={getProgress(file_to_display)}\\n\\t\\t\\t\\t\\t\\tmax=\\"100\\">{getProgress(file_to_display)}</progress\\n\\t\\t\\t\\t\\t>\\n\\t\\t\\t\\t</div>\\n\\t\\t\\t</span>\\n\\t\\t\\t<span class=\\"file-name\\">\\n\\t\\t\\t\\t{file_to_display.orig_name}\\n\\t\\t\\t</span>\\n\\t\\t</div>\\n\\t{/if}\\n</div>\\n\\n<style>\\n\\t.wrap {\\n\\t\\toverflow-y: auto;\\n\\t\\ttransition: opacity 0.5s ease-in-out;\\n\\t\\tbackground: var(--block-background-fill);\\n\\t\\tposition: relative;\\n\\t\\tdisplay: flex;\\n\\t\\tflex-direction: column;\\n\\t\\talign-items: center;\\n\\t\\tjustify-content: center;\\n\\t\\tmin-height: var(--size-40);\\n\\t\\twidth: var(--size-full);\\n\\t}\\n\\n\\t.wrap::after {\\n\\t\\tcontent: \\"\\";\\n\\t\\tposition: absolute;\\n\\t\\ttop: 0;\\n\\t\\tleft: 0;\\n\\t\\twidth: var(--upload-progress-width);\\n\\t\\theight: 100%;\\n\\t\\ttransition: all 0.5s ease-in-out;\\n\\t\\tz-index: 1;\\n\\t}\\n\\n\\t.uploading {\\n\\t\\tfont-size: var(--text-lg);\\n\\t\\tfont-family: var(--font);\\n\\t\\tz-index: 2;\\n\\t}\\n\\n\\t.file-name {\\n\\t\\tmargin: var(--spacing-md);\\n\\t\\tfont-size: var(--text-lg);\\n\\t\\tcolor: var(--body-text-color-subdued);\\n\\t}\\n\\n\\t.file {\\n\\t\\tfont-size: var(--text-md);\\n\\t\\tz-index: 2;\\n\\t\\tdisplay: flex;\\n\\t\\talign-items: center;\\n\\t}\\n\\n\\t.file progress {\\n\\t\\tdisplay: inline;\\n\\t\\theight: var(--size-1);\\n\\t\\twidth: 100%;\\n\\t\\ttransition: all 0.5s ease-in-out;\\n\\t\\tcolor: var(--color-accent);\\n\\t\\tborder: none;\\n\\t}\\n\\n\\t.file progress[value]::-webkit-progress-value {\\n\\t\\tbackground-color: var(--color-accent);\\n\\t\\tborder-radius: 20px;\\n\\t}\\n\\n\\t.file progress[value]::-webkit-progress-bar {\\n\\t\\tbackground-color: var(--border-color-accent);\\n\\t\\tborder-radius: 20px;\\n\\t}\\n\\n\\t.progress-bar {\\n\\t\\twidth: 14px;\\n\\t\\theight: 14px;\\n\\t\\tborder-radius: 50%;\\n\\t\\tbackground: radial-gradient(\\n\\t\\t\\t\\tclosest-side,\\n\\t\\t\\t\\tvar(--block-background-fill) 64%,\\n\\t\\t\\t\\ttransparent 53% 100%\\n\\t\\t\\t),\\n\\t\\t\\tconic-gradient(\\n\\t\\t\\t\\tvar(--color-accent) var(--upload-progress-width),\\n\\t\\t\\t\\tvar(--border-color-accent) 0\\n\\t\\t\\t);\\n\\t\\ttransition: all 0.5s ease-in-out;\\n\\t}</style>\\n"],"names":[],"mappings":"AAwFC,mCAAM,CACL,UAAU,CAAE,IAAI,CAChB,UAAU,CAAE,OAAO,CAAC,IAAI,CAAC,WAAW,CACpC,UAAU,CAAE,IAAI,uBAAuB,CAAC,CACxC,QAAQ,CAAE,QAAQ,CAClB,OAAO,CAAE,IAAI,CACb,cAAc,CAAE,MAAM,CACtB,WAAW,CAAE,MAAM,CACnB,eAAe,CAAE,MAAM,CACvB,UAAU,CAAE,IAAI,SAAS,CAAC,CAC1B,KAAK,CAAE,IAAI,WAAW,CACvB,CAEA,mCAAK,OAAQ,CACZ,OAAO,CAAE,EAAE,CACX,QAAQ,CAAE,QAAQ,CAClB,GAAG,CAAE,CAAC,CACN,IAAI,CAAE,CAAC,CACP,KAAK,CAAE,IAAI,uBAAuB,CAAC,CACnC,MAAM,CAAE,IAAI,CACZ,UAAU,CAAE,GAAG,CAAC,IAAI,CAAC,WAAW,CAChC,OAAO,CAAE,CACV,CAEA,wCAAW,CACV,SAAS,CAAE,IAAI,SAAS,CAAC,CACzB,WAAW,CAAE,IAAI,MAAM,CAAC,CACxB,OAAO,CAAE,CACV,CAEA,wCAAW,CACV,MAAM,CAAE,IAAI,YAAY,CAAC,CACzB,SAAS,CAAE,IAAI,SAAS,CAAC,CACzB,KAAK,CAAE,IAAI,yBAAyB,CACrC,CAEA,mCAAM,CACL,SAAS,CAAE,IAAI,SAAS,CAAC,CACzB,OAAO,CAAE,CAAC,CACV,OAAO,CAAE,IAAI,CACb,WAAW,CAAE,MACd,CAEA,oBAAK,CAAC,uBAAS,CACd,OAAO,CAAE,MAAM,CACf,MAAM,CAAE,IAAI,QAAQ,CAAC,CACrB,KAAK,CAAE,IAAI,CACX,UAAU,CAAE,GAAG,CAAC,IAAI,CAAC,WAAW,CAChC,KAAK,CAAE,IAAI,cAAc,CAAC,CAC1B,MAAM,CAAE,IACT,CAEA,oBAAK,CAAC,QAAQ,CAAC,KAAK,gBAAC,wBAAyB,CAC7C,gBAAgB,CAAE,IAAI,cAAc,CAAC,CACrC,aAAa,CAAE,IAChB,CAEA,oBAAK,CAAC,QAAQ,CAAC,KAAK,gBAAC,sBAAuB,CAC3C,gBAAgB,CAAE,IAAI,qBAAqB,CAAC,CAC5C,aAAa,CAAE,IAChB,CAEA,2CAAc,CACb,KAAK,CAAE,IAAI,CACX,MAAM,CAAE,IAAI,CACZ,aAAa,CAAE,GAAG,CAClB,UAAU,CAAE;AACd,IAAI,YAAY,CAAC;AACjB,IAAI,IAAI,uBAAuB,CAAC,CAAC,GAAG,CAAC;AACrC,IAAI,WAAW,CAAC,GAAG,CAAC,IAAI;AACxB,IAAI,CAAC;AACL,GAAG;AACH,IAAI,IAAI,cAAc,CAAC,CAAC,IAAI,uBAAuB,CAAC,CAAC;AACrD,IAAI,IAAI,qBAAqB,CAAC,CAAC,CAAC;AAChC,IAAI,CACF,UAAU,CAAE,GAAG,CAAC,IAAI,CAAC,WACtB"}'};function D(n){return n.progress*100/(n.size||0)||0}function Ae(n){let t=0;return n.forEach(e=>{t+=D(e);}),document.documentElement.style.setProperty("--upload-progress-width",(t/n.length).toFixed(2)+"%"),t/n.length}const de=create_ssr_component((n,t,e,C)=>{let {upload_id:s}=t,{root:r}=t,{files:A}=t,{stream_handler:l}=t,i,f=A.map(c=>({...c,progress:0}));createEventDispatcher();return onDestroy(()=>{}),t.upload_id===void 0&&e.upload_id&&s!==void 0&&e.upload_id(s),t.root===void 0&&e.root&&r!==void 0&&e.root(r),t.files===void 0&&e.files&&A!==void 0&&e.files(A),t.stream_handler===void 0&&e.stream_handler&&l!==void 0&&e.stream_handler(l),n.css.add(se),Ae(f),i=f[0],`<div class="${["wrap svelte-1vsfomn",""].join(" ").trim()}"><span class="uploading svelte-1vsfomn">Uploading ${escape(f.length)} ${escape(f.length>1?"files":"file")}...</span> ${i?`<div class="file svelte-1vsfomn"><span><div class="progress-bar svelte-1vsfomn"><progress style="visibility:hidden;height:0;width:0;"${add_attribute("value",D(i),0)} max="100" class="svelte-1vsfomn">${escape(D(i))}</progress></div></span> <span class="file-name svelte-1vsfomn">${escape(i.orig_name)}</span></div>`:""} </div>`});function fe(){let n,t;return {drag(e,C={}){t=C;function s(){n=document.createElement("input"),n.type="file",n.style.display="none",n.setAttribute("aria-label","File upload"),n.setAttribute("data-testid","file-upload");const i=Array.isArray(t.accepted_types)?t.accepted_types.join(","):t.accepted_types||void 0;i&&(n.accept=i),n.multiple=t.mode==="multiple"||!1,t.mode==="directory"&&(n.webkitdirectory=!0,n.setAttribute("directory",""),n.setAttribute("mozdirectory","")),e.appendChild(n);}s();function r(i){i.preventDefault(),i.stopPropagation();}function A(i){i.preventDefault(),i.stopPropagation(),t.on_drag_change?.(!0);}function l(i){i.preventDefault(),i.stopPropagation(),t.on_drag_change?.(!1);}function d(i){if(i.preventDefault(),i.stopPropagation(),t.on_drag_change?.(!1),!i.dataTransfer?.files)return;const f=Array.from(i.dataTransfer.files);f.length>0&&t.on_files?.(f);}function u(){t.disable_click||(n.value="",n.click());}function _(){if(n.files){const i=Array.from(n.files);i.length>0&&t.on_files?.(i);}}return e.addEventListener("drag",r),e.addEventListener("dragstart",r),e.addEventListener("dragend",r),e.addEventListener("dragover",r),e.addEventListener("dragenter",A),e.addEventListener("dragleave",l),e.addEventListener("drop",d),e.addEventListener("click",u),n.addEventListener("change",_),{update(i){t=i,n.remove(),s(),n.addEventListener("change",_);},destroy(){e.removeEventListener("drag",r),e.removeEventListener("dragstart",r),e.removeEventListener("dragend",r),e.removeEventListener("dragover",r),e.removeEventListener("dragenter",A),e.removeEventListener("dragleave",l),e.removeEventListener("drop",d),e.removeEventListener("click",u),n.removeEventListener("change",_),n.remove();}}},open_file_upload(){n&&(n.value="",n.click());}}}const ce={code:"button.svelte-edrmkl{cursor:pointer;width:var(--size-full)}.center.svelte-edrmkl{display:flex;justify-content:center}.flex.svelte-edrmkl{display:flex;flex-direction:column;justify-content:center;align-items:center}.hidden.svelte-edrmkl{display:none;position:absolute;flex-grow:0}.hidden.svelte-edrmkl svg{display:none}.disable_click.svelte-edrmkl{cursor:default}.icon-mode.svelte-edrmkl{position:absolute !important;width:var(--size-4);height:var(--size-4);padding:0;min-height:0;border-radius:var(--radius-circle)}.icon-mode.svelte-edrmkl svg{width:var(--size-4);height:var(--size-4)}",map:'{"version":3,"file":"Upload.svelte","sources":["Upload.svelte"],"sourcesContent":["<script lang=\\"ts\\">import { createEventDispatcher, tick, getContext } from \\"svelte\\";\\nimport { prepare_files } from \\"@gradio/client\\";\\nimport { _ } from \\"svelte-i18n\\";\\nimport UploadProgress from \\"./UploadProgress.svelte\\";\\nimport { create_drag } from \\"./utils\\";\\nconst { drag, open_file_upload: _open_file_upload } = create_drag();\\nexport let filetype = null;\\nexport let dragging = false;\\nexport let boundedheight = true;\\nexport let center = true;\\nexport let flex = true;\\nexport let file_count = \\"single\\";\\nexport let disable_click = false;\\nexport let root;\\nexport let hidden = false;\\nexport let format = \\"file\\";\\nexport let uploading = false;\\nexport let show_progress = true;\\nexport let max_file_size = null;\\nexport let upload;\\nexport let stream_handler;\\nexport let icon_upload = false;\\nexport let height = void 0;\\nexport let aria_label = void 0;\\nexport function open_upload() {\\n    _open_file_upload();\\n}\\nlet upload_id;\\nlet file_data;\\nlet accept_file_types;\\nlet use_post_upload_validation = null;\\nconst get_ios = () => {\\n    if (typeof navigator !== \\"undefined\\") {\\n        const userAgent = navigator.userAgent.toLowerCase();\\n        return userAgent.indexOf(\\"iphone\\") > -1 || userAgent.indexOf(\\"ipad\\") > -1;\\n    }\\n    return false;\\n};\\n$: ios = get_ios();\\nconst dispatch = createEventDispatcher();\\nconst validFileTypes = [\\"image\\", \\"video\\", \\"audio\\", \\"text\\", \\"file\\"];\\nconst process_file_type = (type) => {\\n    if (ios && type.startsWith(\\".\\")) {\\n        use_post_upload_validation = true;\\n        return type;\\n    }\\n    if (ios && type.includes(\\"file/*\\")) {\\n        return \\"*\\";\\n    }\\n    if (type.startsWith(\\".\\") || type.endsWith(\\"/*\\")) {\\n        return type;\\n    }\\n    if (validFileTypes.includes(type)) {\\n        return type + \\"/*\\";\\n    }\\n    return \\".\\" + type;\\n};\\n$: if (filetype == null) {\\n    accept_file_types = null;\\n}\\nelse if (typeof filetype === \\"string\\") {\\n    accept_file_types = process_file_type(filetype);\\n}\\nelse if (ios && filetype.includes(\\"file/*\\")) {\\n    accept_file_types = \\"*\\";\\n}\\nelse {\\n    filetype = filetype.map(process_file_type);\\n    accept_file_types = filetype.join(\\", \\");\\n}\\nexport function paste_clipboard() {\\n    navigator.clipboard.read().then(async (items) => {\\n        for (let i = 0; i < items.length; i++) {\\n            const type = items[i].types.find((t) => t.startsWith(\\"image/\\"));\\n            if (type) {\\n                items[i].getType(type).then(async (blob) => {\\n                    const file = new File([blob], `clipboard.${type.replace(\\"image/\\", \\"\\")}`);\\n                    await load_files([file]);\\n                });\\n                break;\\n            }\\n        }\\n    });\\n}\\nexport function open_file_upload() {\\n    _open_file_upload();\\n}\\nasync function handle_upload(file_data2) {\\n    await tick();\\n    upload_id = Math.random().toString(36).substring(2, 15);\\n    uploading = true;\\n    try {\\n        const _file_data = await upload(file_data2, root, upload_id, max_file_size ?? Infinity);\\n        dispatch(\\"load\\", file_count === \\"single\\" ? _file_data?.[0] : _file_data);\\n        uploading = false;\\n        return _file_data || [];\\n    }\\n    catch (e) {\\n        dispatch(\\"error\\", e.message);\\n        uploading = false;\\n        return [];\\n    }\\n}\\nfunction is_valid_mimetype(file_accept, uploaded_file_extension, uploaded_file_type) {\\n    if (!file_accept || file_accept === \\"*\\" || file_accept === \\"file/*\\" || Array.isArray(file_accept) && file_accept.some((accept) => accept === \\"*\\" || accept === \\"file/*\\")) {\\n        return true;\\n    }\\n    let acceptArray;\\n    if (typeof file_accept === \\"string\\") {\\n        acceptArray = file_accept.split(\\",\\").map((s) => s.trim());\\n    }\\n    else if (Array.isArray(file_accept)) {\\n        acceptArray = file_accept;\\n    }\\n    else {\\n        return false;\\n    }\\n    return acceptArray.includes(uploaded_file_extension) || acceptArray.some((type) => {\\n        const [category] = type.split(\\"/\\").map((s) => s.trim());\\n        return type.endsWith(\\"/*\\") && uploaded_file_type.startsWith(category + \\"/\\");\\n    });\\n}\\nexport async function load_files(files) {\\n    if (!files.length) {\\n        return;\\n    }\\n    let _files = files.map((f) => new File([f], f instanceof File ? f.name : \\"file\\", { type: f.type }));\\n    if (ios && use_post_upload_validation) {\\n        _files = _files.filter((file) => {\\n            if (is_valid_file(file)) {\\n                return true;\\n            }\\n            dispatch(\\"error\\", `Invalid file type: ${file.name}. Only ${filetype} allowed.`);\\n            return false;\\n        });\\n        if (_files.length === 0) {\\n            return [];\\n        }\\n    }\\n    file_data = await prepare_files(_files);\\n    return await handle_upload(file_data);\\n}\\nfunction is_valid_file(file) {\\n    if (!filetype)\\n        return true;\\n    const allowed_types = Array.isArray(filetype) ? filetype : [filetype];\\n    return allowed_types.some((type) => {\\n        const processed_type = process_file_type(type);\\n        if (processed_type.startsWith(\\".\\")) {\\n            return file.name.toLowerCase().endsWith(processed_type.toLowerCase());\\n        }\\n        if (processed_type === \\"*\\") {\\n            return true;\\n        }\\n        if (processed_type.endsWith(\\"/*\\")) {\\n            const [category] = processed_type.split(\\"/\\");\\n            return file.type.startsWith(category + \\"/\\");\\n        }\\n        return file.type === processed_type;\\n    });\\n}\\nasync function load_files_from_upload(files) {\\n    const files_to_load = files.filter((file) => {\\n        const file_extension = \\".\\" + file.name.split(\\".\\").pop();\\n        if (file_extension && is_valid_mimetype(accept_file_types, file_extension, file.type)) {\\n            return true;\\n        }\\n        if (file_extension && Array.isArray(filetype) ? filetype.includes(file_extension) : file_extension === filetype) {\\n            return true;\\n        }\\n        dispatch(\\"error\\", `Invalid file type only ${filetype} allowed.`);\\n        return false;\\n    });\\n    if (format != \\"blob\\") {\\n        await load_files(files_to_load);\\n    }\\n    else {\\n        if (file_count === \\"single\\") {\\n            dispatch(\\"load\\", files_to_load[0]);\\n            return;\\n        }\\n        dispatch(\\"load\\", files_to_load);\\n    }\\n}\\nexport async function load_files_from_drop(e) {\\n    dragging = false;\\n    if (!e.dataTransfer?.files)\\n        return;\\n    const files_to_load = Array.from(e.dataTransfer.files).filter(is_valid_file);\\n    if (format != \\"blob\\") {\\n        await load_files(files_to_load);\\n    }\\n    else {\\n        if (file_count === \\"single\\") {\\n            dispatch(\\"load\\", files_to_load[0]);\\n            return;\\n        }\\n        dispatch(\\"load\\", files_to_load);\\n    }\\n}\\n<\/script>\\n\\n{#if filetype === \\"clipboard\\"}\\n\\t<button\\n\\t\\tclass:hidden\\n\\t\\tclass:center\\n\\t\\tclass:boundedheight\\n\\t\\tclass:flex\\n\\t\\tclass:icon-mode={icon_upload}\\n\\t\\tstyle:height={icon_upload\\n\\t\\t\\t? \\"\\"\\n\\t\\t\\t: height\\n\\t\\t\\t\\t? typeof height === \\"number\\"\\n\\t\\t\\t\\t\\t? height + \\"px\\"\\n\\t\\t\\t\\t\\t: height\\n\\t\\t\\t\\t: \\"100%\\"}\\n\\t\\ttabindex={hidden ? -1 : 0}\\n\\t\\ton:click={paste_clipboard}\\n\\t\\taria-label={aria_label || \\"Paste from clipboard\\"}\\n\\t>\\n\\t\\t<slot />\\n\\t</button>\\n{:else if uploading && show_progress}\\n\\t{#if !hidden}\\n\\t\\t<UploadProgress {root} {upload_id} files={file_data} {stream_handler} />\\n\\t{/if}\\n{:else}\\n\\t<button\\n\\t\\tclass:hidden\\n\\t\\tclass:center\\n\\t\\tclass:boundedheight\\n\\t\\tclass:flex\\n\\t\\tclass:disable_click\\n\\t\\tclass:icon-mode={icon_upload}\\n\\t\\tstyle:height={icon_upload\\n\\t\\t\\t? \\"\\"\\n\\t\\t\\t: height\\n\\t\\t\\t\\t? typeof height === \\"number\\"\\n\\t\\t\\t\\t\\t? height + \\"px\\"\\n\\t\\t\\t\\t\\t: height\\n\\t\\t\\t\\t: \\"100%\\"}\\n\\t\\ttabindex={hidden ? -1 : 0}\\n\\t\\tuse:drag={{\\n\\t\\t\\ton_drag_change: (dragging) => (dragging = dragging),\\n\\t\\t\\ton_files: (files) => load_files_from_upload(files),\\n\\t\\t\\taccepted_types: accept_file_types,\\n\\t\\t\\tmode: file_count,\\n\\t\\t\\tdisable_click\\n\\t\\t}}\\n\\t\\taria-label={aria_label || \\"Click to upload or drop files\\"}\\n\\t\\taria-dropeffect=\\"copy\\"\\n\\t>\\n\\t\\t<slot />\\n\\t</button>\\n{/if}\\n\\n<style>\\n\\tbutton {\\n\\t\\tcursor: pointer;\\n\\t\\twidth: var(--size-full);\\n\\t}\\n\\n\\t.center {\\n\\t\\tdisplay: flex;\\n\\t\\tjustify-content: center;\\n\\t}\\n\\t.flex {\\n\\t\\tdisplay: flex;\\n\\t\\tflex-direction: column;\\n\\t\\tjustify-content: center;\\n\\t\\talign-items: center;\\n\\t}\\n\\t.hidden {\\n\\t\\tdisplay: none;\\n\\t\\tposition: absolute;\\n\\t\\tflex-grow: 0;\\n\\t}\\n\\n\\t.hidden :global(svg) {\\n\\t\\tdisplay: none;\\n\\t}\\n\\n\\t.disable_click {\\n\\t\\tcursor: default;\\n\\t}\\n\\n\\t.icon-mode {\\n\\t\\tposition: absolute !important;\\n\\t\\twidth: var(--size-4);\\n\\t\\theight: var(--size-4);\\n\\t\\tpadding: 0;\\n\\t\\tmin-height: 0;\\n\\t\\tborder-radius: var(--radius-circle);\\n\\t}\\n\\n\\t.icon-mode :global(svg) {\\n\\t\\twidth: var(--size-4);\\n\\t\\theight: var(--size-4);\\n\\t}</style>\\n"],"names":[],"mappings":"AAiQC,oBAAO,CACN,MAAM,CAAE,OAAO,CACf,KAAK,CAAE,IAAI,WAAW,CACvB,CAEA,qBAAQ,CACP,OAAO,CAAE,IAAI,CACb,eAAe,CAAE,MAClB,CACA,mBAAM,CACL,OAAO,CAAE,IAAI,CACb,cAAc,CAAE,MAAM,CACtB,eAAe,CAAE,MAAM,CACvB,WAAW,CAAE,MACd,CACA,qBAAQ,CACP,OAAO,CAAE,IAAI,CACb,QAAQ,CAAE,QAAQ,CAClB,SAAS,CAAE,CACZ,CAEA,qBAAO,CAAS,GAAK,CACpB,OAAO,CAAE,IACV,CAEA,4BAAe,CACd,MAAM,CAAE,OACT,CAEA,wBAAW,CACV,QAAQ,CAAE,QAAQ,CAAC,UAAU,CAC7B,KAAK,CAAE,IAAI,QAAQ,CAAC,CACpB,MAAM,CAAE,IAAI,QAAQ,CAAC,CACrB,OAAO,CAAE,CAAC,CACV,UAAU,CAAE,CAAC,CACb,aAAa,CAAE,IAAI,eAAe,CACnC,CAEA,wBAAU,CAAS,GAAK,CACvB,KAAK,CAAE,IAAI,QAAQ,CAAC,CACpB,MAAM,CAAE,IAAI,QAAQ,CACrB"}'},ge=create_ssr_component((n,t,e,C)=>{let s;const{drag:r,open_file_upload:A}=fe();let{filetype:l=null}=t,{dragging:d=!1}=t,{boundedheight:u=!0}=t,{center:_=!0}=t,{flex:i=!0}=t,{file_count:f="single"}=t,{disable_click:x=!1}=t,{root:I}=t,{hidden:c=!1}=t,{format:v="file"}=t,{uploading:g=!1}=t,{show_progress:W=!0}=t,{max_file_size:O=null}=t,{upload:U}=t,{stream_handler:P}=t,{icon_upload:E=!1}=t,{height:h=void 0}=t,{aria_label:B=void 0}=t;function F(){A();}let Q,L,j=null;const X=()=>{if(typeof navigator<"u"){const a=navigator.userAgent.toLowerCase();return a.indexOf("iphone")>-1||a.indexOf("ipad")>-1}return !1},w=createEventDispatcher(),J=["image","video","audio","text","file"],K=a=>s&&a.startsWith(".")?(j=!0,a):s&&a.includes("file/*")?"*":a.startsWith(".")||a.endsWith("/*")?a:J.includes(a)?a+"/*":"."+a;function q(){navigator.clipboard.read().then(async a=>{for(let o=0;o<a.length;o++){const p=a[o].types.find(m=>m.startsWith("image/"));if(p){a[o].getType(p).then(async m=>{const S=new File([m],`clipboard.${p.replace("image/","")}`);await k([S]);});break}}});}function V(){A();}async function R(a){await tick(),Q=Math.random().toString(36).substring(2,15),g=!0;try{const o=await U(a,I,Q,O??1/0);return w("load",f==="single"?o?.[0]:o),g=!1,o||[]}catch(o){return w("error",o.message),g=!1,[]}}async function k(a){if(!a.length)return;let o=a.map(p=>new File([p],p instanceof File?p.name:"file",{type:p.type}));return s&&j&&(o=o.filter(p=>N(p)?!0:(w("error",`Invalid file type: ${p.name}. Only ${l} allowed.`),!1)),o.length===0)?[]:(L=await ta(o),await R(L))}function N(a){return l?(Array.isArray(l)?l:[l]).some(p=>{const m=K(p);if(m.startsWith("."))return a.name.toLowerCase().endsWith(m.toLowerCase());if(m==="*")return !0;if(m.endsWith("/*")){const[S]=m.split("/");return a.type.startsWith(S+"/")}return a.type===m}):!0}async function Y(a){if(d=!1,!a.dataTransfer?.files)return;const o=Array.from(a.dataTransfer.files).filter(N);if(v!="blob")await k(o);else {if(f==="single"){w("load",o[0]);return}w("load",o);}}return t.filetype===void 0&&e.filetype&&l!==void 0&&e.filetype(l),t.dragging===void 0&&e.dragging&&d!==void 0&&e.dragging(d),t.boundedheight===void 0&&e.boundedheight&&u!==void 0&&e.boundedheight(u),t.center===void 0&&e.center&&_!==void 0&&e.center(_),t.flex===void 0&&e.flex&&i!==void 0&&e.flex(i),t.file_count===void 0&&e.file_count&&f!==void 0&&e.file_count(f),t.disable_click===void 0&&e.disable_click&&x!==void 0&&e.disable_click(x),t.root===void 0&&e.root&&I!==void 0&&e.root(I),t.hidden===void 0&&e.hidden&&c!==void 0&&e.hidden(c),t.format===void 0&&e.format&&v!==void 0&&e.format(v),t.uploading===void 0&&e.uploading&&g!==void 0&&e.uploading(g),t.show_progress===void 0&&e.show_progress&&W!==void 0&&e.show_progress(W),t.max_file_size===void 0&&e.max_file_size&&O!==void 0&&e.max_file_size(O),t.upload===void 0&&e.upload&&U!==void 0&&e.upload(U),t.stream_handler===void 0&&e.stream_handler&&P!==void 0&&e.stream_handler(P),t.icon_upload===void 0&&e.icon_upload&&E!==void 0&&e.icon_upload(E),t.height===void 0&&e.height&&h!==void 0&&e.height(h),t.aria_label===void 0&&e.aria_label&&B!==void 0&&e.aria_label(B),t.open_upload===void 0&&e.open_upload&&F!==void 0&&e.open_upload(F),t.paste_clipboard===void 0&&e.paste_clipboard&&q!==void 0&&e.paste_clipboard(q),t.open_file_upload===void 0&&e.open_file_upload&&V!==void 0&&e.open_file_upload(V),t.load_files===void 0&&e.load_files&&k!==void 0&&e.load_files(k),t.load_files_from_drop===void 0&&e.load_files_from_drop&&Y!==void 0&&e.load_files_from_drop(Y),n.css.add(ce),s=X(),l==null||(typeof l=="string"?K(l):s&&l.includes("file/*")||(l=l.map(K),l.join(", "))),`${l==="clipboard"?`<button${add_attribute("tabindex",c?-1:0,0)}${add_attribute("aria-label",B||"Paste from clipboard",0)} class="${["svelte-edrmkl",(c?"hidden":"")+" "+(_?"center":"")+" "+(u?"boundedheight":"")+" "+(i?"flex":"")+" "+(E?"icon-mode":"")].join(" ").trim()}"${add_styles({height:E?"":h?typeof h=="number"?h+"px":h:"100%"})}>${C.default?C.default({}):""}</button>`:`${g&&W?`${c?"":`${validate_component(de,"UploadProgress").$$render(n,{root:I,upload_id:Q,files:L,stream_handler:P},{},{})}`}`:`<button${add_attribute("tabindex",c?-1:0,0)}${add_attribute("aria-label",B||"Click to upload or drop files",0)} aria-dropeffect="copy" class="${["svelte-edrmkl",(c?"hidden":"")+" "+(_?"center":"")+" "+(u?"boundedheight":"")+" "+(i?"flex":"")+" "+(x?"disable_click":"")+" "+(E?"icon-mode":"")].join(" ").trim()}"${add_styles({height:E?"":h?typeof h=="number"?h+"px":h:"100%"})}>${C.default?C.default({}):""}</button>`}`}`}),he=create_ssr_component((n,t,e,C$1)=>{let{editable:s=!1}=t,{undoable:r=!1}=t,{download:A=null}=t,{i18n:l}=t;return createEventDispatcher(),t.editable===void 0&&e.editable&&s!==void 0&&e.editable(s),t.undoable===void 0&&e.undoable&&r!==void 0&&e.undoable(r),t.download===void 0&&e.download&&A!==void 0&&e.download(A),t.i18n===void 0&&e.i18n&&l!==void 0&&e.i18n(l),`${validate_component(Ke,"IconButtonWrapper").$$render(n,{},{},{default:()=>`${s?`${validate_component(j,"IconButton").$$render(n,{Icon:Vt,label:l("common.edit")},{},{})}`:""} ${r?`${validate_component(j,"IconButton").$$render(n,{Icon:pe,label:l("common.undo")},{},{})}`:""} ${A?`${validate_component(C,"DownloadLink").$$render(n,{href:A,download:!0},{},{default:()=>`${validate_component(j,"IconButton").$$render(n,{Icon:Ut,label:l("common.download")},{},{})}`})}`:""} ${C$1.default?C$1.default({}):""} ${validate_component(j,"IconButton").$$render(n,{Icon:qt,label:l("common.clear")},{},{})}`})}`});

export { ge as g, he as h };
//# sourceMappingURL=ModifyUpload-Ca-f84bx.js.map
