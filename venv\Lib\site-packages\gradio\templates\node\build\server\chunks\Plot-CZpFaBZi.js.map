{"version": 3, "file": "Plot-CZpFaBZi.js", "sources": ["../../../../../../js/app/.svelte-kit/adapter-node/chunks/Plot.js"], "sourcesContent": ["import{create_ssr_component as z,validate_component as y,missing_component as C}from\"svelte/internal\";import{E as D,P as I}from\"./FullscreenButton.js\";import{createEventDispatcher as j}from\"svelte\";const G=z((_,e,t,q)=>{let{value:l}=e,s,{colors:i=[]}=e,{show_label:d}=e,{theme_mode:v}=e,{caption:c}=e,{bokeh_version:m}=e,{show_actions_button:h}=e,{gradio:f}=e,{x_lim:r=null}=e,{_selectable:u}=e,a=null,x=l?.type,P=!1;const M=j(),k={plotly:()=>import(\"./PlotlyPlot.js\"),bokeh:()=>import(\"./BokehPlot.js\"),altair:()=>import(\"./AltairPlot.js\"),matplotlib:()=>import(\"./MatplotlibPlot.js\")};let n={};const T=typeof window<\"u\";e.value===void 0&&t.value&&l!==void 0&&t.value(l),e.colors===void 0&&t.colors&&i!==void 0&&t.colors(i),e.show_label===void 0&&t.show_label&&d!==void 0&&t.show_label(d),e.theme_mode===void 0&&t.theme_mode&&v!==void 0&&t.theme_mode(v),e.caption===void 0&&t.caption&&c!==void 0&&t.caption(c),e.bokeh_version===void 0&&t.bokeh_version&&m!==void 0&&t.bokeh_version(m),e.show_actions_button===void 0&&t.show_actions_button&&h!==void 0&&t.show_actions_button(h),e.gradio===void 0&&t.gradio&&f!==void 0&&t.gradio(f),e.x_lim===void 0&&t.x_lim&&r!==void 0&&t.x_lim(r),e._selectable===void 0&&t._selectable&&u!==void 0&&t._selectable(u);let w,E,b=_.head;do{if(w=!0,_.head=b,l!==s){let o=l?.type;o!==x&&(a=null),o&&o in k&&T&&(n[o]?a=n[o]:k[o]().then(p=>{a=p.default,n[o]=a})),s=l,x=o,M(\"change\")}E=`${l&&a?`${y(a||C,\"svelte:component\").$$render(_,{value:l,colors:i,theme_mode:v,show_label:d,caption:c,bokeh_version:m,show_actions_button:h,gradio:f,_selectable:u,x_lim:r,loaded_plotly_css:P},{loaded_plotly_css:o=>{P=o,w=!1}},{})}`:`${y(D,\"Empty\").$$render(_,{unpadded_box:!0,size:\"large\"},{},{default:()=>`${y(I,\"PlotIcon\").$$render(_,{},{},{})}`})}`}`}while(!w);return E});export{G as default};\n//# sourceMappingURL=Plot.js.map\n"], "names": ["z", "j", "y", "C", "D", "I"], "mappings": ";;;;;;;;;AAA2M,MAAC,CAAC,CAACA,oBAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAACC,qBAAC,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,OAAO,0BAAiB,CAAC,CAAC,KAAK,CAAC,IAAI,OAAO,yBAAgB,CAAC,CAAC,MAAM,CAAC,IAAI,OAAO,0BAAiB,CAAC,CAAC,UAAU,CAAC,IAAI,OAAO,8BAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,aAAa,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,mBAAmB,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,mBAAmB,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAEC,kBAAC,CAAC,CAAC,EAAEC,iBAAC,CAAC,kBAAkB,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAED,kBAAC,CAACE,EAAC,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,EAAEF,kBAAC,CAACG,EAAC,CAAC,UAAU,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,MAAM,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;;;;"}