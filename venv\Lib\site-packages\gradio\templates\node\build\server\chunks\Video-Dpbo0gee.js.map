{"version": 3, "file": "Video-Dpbo0gee.js", "sources": ["../../../../../../js/app/.svelte-kit/adapter-node/chunks/Video.js"], "sourcesContent": ["import{create_ssr_component as R,add_attribute as r}from\"svelte/internal\";import{createEventDispatcher as k}from\"svelte\";import{r as T}from\"./DownloadLink.js\";import{H as A}from\"./hls.js\";const K={code:`.overlay.svelte-1y0s5gv{position:absolute;background-color:rgba(0, 0, 0, 0.4);width:100%;height:100%}.hidden.svelte-1y0s5gv{display:none}.load-wrap.svelte-1y0s5gv{display:flex;justify-content:center;align-items:center;height:100%}.loader.svelte-1y0s5gv{display:flex;position:relative;background-color:var(--border-color-accent-subdued);animation:svelte-1y0s5gv-shadowPulse 2s linear infinite;box-shadow:-24px 0 var(--border-color-accent-subdued),\n\t\t\t24px 0 var(--border-color-accent-subdued);margin:var(--spacing-md);border-radius:50%;width:10px;height:10px;scale:0.5}@keyframes svelte-1y0s5gv-shadowPulse{33%{box-shadow:-24px 0 var(--border-color-accent-subdued),\n\t\t\t\t24px 0 #fff;background:#fff}66%{box-shadow:-24px 0 #fff,\n\t\t\t\t24px 0 #fff;background:var(--border-color-accent-subdued)}100%{box-shadow:-24px 0 #fff,\n\t\t\t\t24px 0 var(--border-color-accent-subdued);background:#fff}}`,map:'{\"version\":3,\"file\":\"Video.svelte\",\"sources\":[\"Video.svelte\"],\"sourcesContent\":[\"<script lang=\\\\\"ts\\\\\">import { createEventDispatcher } from \\\\\"svelte\\\\\";\\\\nimport { loaded } from \\\\\"./utils\\\\\";\\\\nimport { resolve_wasm_src } from \\\\\"@gradio/wasm/svelte\\\\\";\\\\nimport Hls from \\\\\"hls.js\\\\\";\\\\nexport let src = void 0;\\\\nexport let muted = void 0;\\\\nexport let playsinline = void 0;\\\\nexport let preload = void 0;\\\\nexport let autoplay = void 0;\\\\nexport let controls = void 0;\\\\nexport let currentTime = void 0;\\\\nexport let duration = void 0;\\\\nexport let paused = void 0;\\\\nexport let node = void 0;\\\\nexport let loop;\\\\nexport let is_stream;\\\\nexport let processingVideo = false;\\\\nlet resolved_src;\\\\nlet stream_active = false;\\\\nlet latest_src;\\\\n$: {\\\\n    resolved_src = src;\\\\n    latest_src = src;\\\\n    const resolving_src = src;\\\\n    resolve_wasm_src(resolving_src).then((s) => {\\\\n        if (latest_src === resolving_src) {\\\\n            resolved_src = s;\\\\n        }\\\\n    });\\\\n}\\\\nconst dispatch = createEventDispatcher();\\\\nfunction load_stream(src2, is_stream2, node2) {\\\\n    if (!src2 || !is_stream2)\\\\n        return;\\\\n    if (Hls.isSupported() && !stream_active) {\\\\n        const hls = new Hls({\\\\n            maxBufferLength: 1,\\\\n            // 0.5 seconds (500 ms)\\\\n            maxMaxBufferLength: 1,\\\\n            // Maximum max buffer length in seconds\\\\n            lowLatencyMode: true\\\\n            // Enable low latency mode\\\\n        });\\\\n        hls.loadSource(src2);\\\\n        hls.attachMedia(node2);\\\\n        hls.on(Hls.Events.MANIFEST_PARSED, function () {\\\\n            node2.play();\\\\n        });\\\\n        hls.on(Hls.Events.ERROR, function (event, data) {\\\\n            console.error(\\\\\"HLS error:\\\\\", event, data);\\\\n            if (data.fatal) {\\\\n                switch (data.type) {\\\\n                    case Hls.ErrorTypes.NETWORK_ERROR:\\\\n                        console.error(\\\\\"Fatal network error encountered, trying to recover\\\\\");\\\\n                        hls.startLoad();\\\\n                        break;\\\\n                    case Hls.ErrorTypes.MEDIA_ERROR:\\\\n                        console.error(\\\\\"Fatal media error encountered, trying to recover\\\\\");\\\\n                        hls.recoverMediaError();\\\\n                        break;\\\\n                    default:\\\\n                        console.error(\\\\\"Fatal error, cannot recover\\\\\");\\\\n                        hls.destroy();\\\\n                        break;\\\\n                }\\\\n            }\\\\n        });\\\\n        stream_active = true;\\\\n    }\\\\n}\\\\n$: src, stream_active = false;\\\\n$: if (node && src && is_stream) {\\\\n    load_stream(src, is_stream, node);\\\\n}\\\\n<\\/script>\\\\n\\\\n<!--\\\\nThe spread operator with `$$props` or `$$restProps` can\\'t be used here\\\\nto pass props from the parent component to the <video> element\\\\nbecause of its unexpected behavior: https://github.com/sveltejs/svelte/issues/7404\\\\nFor example, if we add {...$$props} or {...$$restProps}, the boolean props aside it like `controls` will be compiled as string \\\\\"true\\\\\" or \\\\\"false\\\\\" on the actual DOM.\\\\nThen, even when `controls` is false, the compiled DOM would be `<video controls=\\\\\"false\\\\\">` which is equivalent to `<video controls>` since the string \\\\\"false\\\\\" is even truthy.\\\\n-->\\\\n<div class:hidden={!processingVideo} class=\\\\\"overlay\\\\\">\\\\n\\\\t<span class=\\\\\"load-wrap\\\\\">\\\\n\\\\t\\\\t<span class=\\\\\"loader\\\\\" />\\\\n\\\\t</span>\\\\n</div>\\\\n<video\\\\n\\\\tsrc={resolved_src}\\\\n\\\\t{muted}\\\\n\\\\t{playsinline}\\\\n\\\\t{preload}\\\\n\\\\t{autoplay}\\\\n\\\\t{controls}\\\\n\\\\t{loop}\\\\n\\\\ton:loadeddata={dispatch.bind(null, \\\\\"loadeddata\\\\\")}\\\\n\\\\ton:click={dispatch.bind(null, \\\\\"click\\\\\")}\\\\n\\\\ton:play={dispatch.bind(null, \\\\\"play\\\\\")}\\\\n\\\\ton:pause={dispatch.bind(null, \\\\\"pause\\\\\")}\\\\n\\\\ton:ended={dispatch.bind(null, \\\\\"ended\\\\\")}\\\\n\\\\ton:mouseover={dispatch.bind(null, \\\\\"mouseover\\\\\")}\\\\n\\\\ton:mouseout={dispatch.bind(null, \\\\\"mouseout\\\\\")}\\\\n\\\\ton:focus={dispatch.bind(null, \\\\\"focus\\\\\")}\\\\n\\\\ton:blur={dispatch.bind(null, \\\\\"blur\\\\\")}\\\\n\\\\ton:error={dispatch.bind(null, \\\\\"error\\\\\", \\\\\"Video not playable\\\\\")}\\\\n\\\\ton:loadstart\\\\n\\\\ton:loadeddata\\\\n\\\\ton:loadedmetadata\\\\n\\\\tbind:currentTime\\\\n\\\\tbind:duration\\\\n\\\\tbind:paused\\\\n\\\\tbind:this={node}\\\\n\\\\tuse:loaded={{ autoplay: autoplay ?? false }}\\\\n\\\\tdata-testid={$$props[\\\\\"data-testid\\\\\"]}\\\\n\\\\tcrossorigin=\\\\\"anonymous\\\\\"\\\\n>\\\\n\\\\t<slot />\\\\n</video>\\\\n\\\\n<style>\\\\n\\\\t.overlay {\\\\n\\\\t\\\\tposition: absolute;\\\\n\\\\t\\\\tbackground-color: rgba(0, 0, 0, 0.4);\\\\n\\\\t\\\\twidth: 100%;\\\\n\\\\t\\\\theight: 100%;\\\\n\\\\t}\\\\n\\\\n\\\\t.hidden {\\\\n\\\\t\\\\tdisplay: none;\\\\n\\\\t}\\\\n\\\\n\\\\t.load-wrap {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tjustify-content: center;\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\theight: 100%;\\\\n\\\\t}\\\\n\\\\n\\\\t.loader {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tposition: relative;\\\\n\\\\t\\\\tbackground-color: var(--border-color-accent-subdued);\\\\n\\\\t\\\\tanimation: shadowPulse 2s linear infinite;\\\\n\\\\t\\\\tbox-shadow:\\\\n\\\\t\\\\t\\\\t-24px 0 var(--border-color-accent-subdued),\\\\n\\\\t\\\\t\\\\t24px 0 var(--border-color-accent-subdued);\\\\n\\\\t\\\\tmargin: var(--spacing-md);\\\\n\\\\t\\\\tborder-radius: 50%;\\\\n\\\\t\\\\twidth: 10px;\\\\n\\\\t\\\\theight: 10px;\\\\n\\\\t\\\\tscale: 0.5;\\\\n\\\\t}\\\\n\\\\n\\\\t@keyframes shadowPulse {\\\\n\\\\t\\\\t33% {\\\\n\\\\t\\\\t\\\\tbox-shadow:\\\\n\\\\t\\\\t\\\\t\\\\t-24px 0 var(--border-color-accent-subdued),\\\\n\\\\t\\\\t\\\\t\\\\t24px 0 #fff;\\\\n\\\\t\\\\t\\\\tbackground: #fff;\\\\n\\\\t\\\\t}\\\\n\\\\t\\\\t66% {\\\\n\\\\t\\\\t\\\\tbox-shadow:\\\\n\\\\t\\\\t\\\\t\\\\t-24px 0 #fff,\\\\n\\\\t\\\\t\\\\t\\\\t24px 0 #fff;\\\\n\\\\t\\\\t\\\\tbackground: var(--border-color-accent-subdued);\\\\n\\\\t\\\\t}\\\\n\\\\t\\\\t100% {\\\\n\\\\t\\\\t\\\\tbox-shadow:\\\\n\\\\t\\\\t\\\\t\\\\t-24px 0 #fff,\\\\n\\\\t\\\\t\\\\t\\\\t24px 0 var(--border-color-accent-subdued);\\\\n\\\\t\\\\t\\\\tbackground: #fff;\\\\n\\\\t\\\\t}\\\\n\\\\t}</style>\\\\n\"],\"names\":[],\"mappings\":\"AAyHC,uBAAS,CACR,QAAQ,CAAE,QAAQ,CAClB,gBAAgB,CAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CACpC,KAAK,CAAE,IAAI,CACX,MAAM,CAAE,IACT,CAEA,sBAAQ,CACP,OAAO,CAAE,IACV,CAEA,yBAAW,CACV,OAAO,CAAE,IAAI,CACb,eAAe,CAAE,MAAM,CACvB,WAAW,CAAE,MAAM,CACnB,MAAM,CAAE,IACT,CAEA,sBAAQ,CACP,OAAO,CAAE,IAAI,CACb,QAAQ,CAAE,QAAQ,CAClB,gBAAgB,CAAE,IAAI,6BAA6B,CAAC,CACpD,SAAS,CAAE,0BAAW,CAAC,EAAE,CAAC,MAAM,CAAC,QAAQ,CACzC,UAAU,CACT,KAAK,CAAC,CAAC,CAAC,IAAI,6BAA6B,CAAC,CAAC;AAC9C,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,6BAA6B,CAAC,CAC1C,MAAM,CAAE,IAAI,YAAY,CAAC,CACzB,aAAa,CAAE,GAAG,CAClB,KAAK,CAAE,IAAI,CACX,MAAM,CAAE,IAAI,CACZ,KAAK,CAAE,GACR,CAEA,WAAW,0BAAY,CACtB,GAAI,CACH,UAAU,CACT,KAAK,CAAC,CAAC,CAAC,IAAI,6BAA6B,CAAC,CAAC;AAC/C,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CACZ,UAAU,CAAE,IACb,CACA,GAAI,CACH,UAAU,CACT,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC;AACjB,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CACZ,UAAU,CAAE,IAAI,6BAA6B,CAC9C,CACA,IAAK,CACJ,UAAU,CACT,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC;AACjB,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,6BAA6B,CAAC,CAC1C,UAAU,CAAE,IACb,CACD\"}'},V=R((w,e,t,x)=>{let{src:o=void 0}=e,{muted:d=void 0}=e,{playsinline:i=void 0}=e,{preload:c=void 0}=e,{autoplay:C=void 0}=e,{controls:u=void 0}=e,{currentTime:v=void 0}=e,{duration:E=void 0}=e,{paused:p=void 0}=e,{node:a=void 0}=e,{loop:f}=e,{is_stream:l}=e,{processingVideo:m=!1}=e,h,y=!1,_;k();function B(s,b,g){if(!(!s||!b)&&A.isSupported()&&!y){const n=new A({maxBufferLength:1,maxMaxBufferLength:1,lowLatencyMode:!0});n.loadSource(s),n.attachMedia(g),n.on(A.Events.MANIFEST_PARSED,function(){g.play()}),n.on(A.Events.ERROR,function(M,I){if(console.error(\"HLS error:\",M,I),I.fatal)switch(I.type){case A.ErrorTypes.NETWORK_ERROR:console.error(\"Fatal network error encountered, trying to recover\"),n.startLoad();break;case A.ErrorTypes.MEDIA_ERROR:console.error(\"Fatal media error encountered, trying to recover\"),n.recoverMediaError();break;default:console.error(\"Fatal error, cannot recover\"),n.destroy();break}}),y=!0}}e.src===void 0&&t.src&&o!==void 0&&t.src(o),e.muted===void 0&&t.muted&&d!==void 0&&t.muted(d),e.playsinline===void 0&&t.playsinline&&i!==void 0&&t.playsinline(i),e.preload===void 0&&t.preload&&c!==void 0&&t.preload(c),e.autoplay===void 0&&t.autoplay&&C!==void 0&&t.autoplay(C),e.controls===void 0&&t.controls&&u!==void 0&&t.controls(u),e.currentTime===void 0&&t.currentTime&&v!==void 0&&t.currentTime(v),e.duration===void 0&&t.duration&&E!==void 0&&t.duration(E),e.paused===void 0&&t.paused&&p!==void 0&&t.paused(p),e.node===void 0&&t.node&&a!==void 0&&t.node(a),e.loop===void 0&&t.loop&&f!==void 0&&t.loop(f),e.is_stream===void 0&&t.is_stream&&l!==void 0&&t.is_stream(l),e.processingVideo===void 0&&t.processingVideo&&m!==void 0&&t.processingVideo(m),w.css.add(K);{h=o,_=o;const s=o;T(s).then(b=>{_===s&&(h=b)})}return y=!1,a&&o&&l&&B(o,l,a),` <div class=\"${[\"overlay svelte-1y0s5gv\",m?\"\":\"hidden\"].join(\" \").trim()}\" data-svelte-h=\"svelte-mez4j5\"><span class=\"load-wrap svelte-1y0s5gv\"><span class=\"loader svelte-1y0s5gv\"></span></span></div> <video${r(\"src\",h,0)} ${d?\"muted\":\"\"} ${i?\"playsinline\":\"\"}${r(\"preload\",c,0)} ${C?\"autoplay\":\"\"} ${u?\"controls\":\"\"} ${f?\"loop\":\"\"}${r(\"data-testid\",e[\"data-testid\"],0)} crossorigin=\"anonymous\"${r(\"currentTime\",v,0)}${r(\"paused\",p,0)}${r(\"this\",a,0)}>${x.default?x.default({}):\"\"}</video>`});export{V};\n//# sourceMappingURL=Video.js.map\n"], "names": ["R", "k", "A", "T", "r"], "mappings": ";;;;AAAiM,MAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;AAC3M;AACA;AACA;AACA,+DAA+D,CAAC,CAAC,GAAG,CAAC,wnNAAwnN,CAAC,CAAC,CAAC,CAACA,oBAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAACC,qBAAC,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAEC,EAAC,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAIA,EAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAACA,EAAC,CAAC,MAAM,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,GAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAACA,EAAC,CAAC,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,KAAKA,EAAC,CAAC,UAAU,CAAC,aAAa,CAAC,OAAO,CAAC,KAAK,CAAC,oDAAoD,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC,MAAM,KAAKA,EAAC,CAAC,UAAU,CAAC,WAAW,CAAC,OAAO,CAAC,KAAK,CAAC,kDAAkD,CAAC,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC,MAAM,QAAQ,OAAO,CAAC,KAAK,CAAC,6BAA6B,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,SAAS,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,eAAe,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAACC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,EAAE,CAAC,wBAAwB,CAAC,CAAC,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,sIAAsI,EAAEC,aAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,aAAa,CAAC,EAAE,CAAC,EAAEA,aAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,EAAEA,aAAC,CAAC,aAAa,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,wBAAwB,EAAEA,aAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEA,aAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEA,aAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC;;;;"}