import { c as create_ssr_component, s as subscribe, b as createEventDispatcher, o as onDestroy, d as add_attribute, f as each, v as validate_component, m as missing_component } from './ssr-C3HYbsxA.js';
import { w as writable } from './index-ClteBeTX.js';
import { d as a, F as Fs } from './2-CUxBFVNo.js';
import { b } from './stores-D_Cz4lHU.js';
import './Component-NmRBwSfF.js';
import 'tty';
import 'path';
import 'url';
import 'fs';
import './exports-Cu4i9J_Z.js';

var te=()=>{const t=document.createElement("link");t.href="https://fonts.googleapis.com/css2?family=Source+Sans+Pro:ital,wght@0,200;0,300;0,400;0,600;0,700;0,900;1,200;1,300;1,400;1,600;1,700;1,900&display=swap",t.rel="stylesheet";const e=document.createElement("link");e.href="https://fonts.googleapis.com/css2?family=IBM+Plex+Mono:wght@400;600;700&display=swap",e.rel="stylesheet",document.head.appendChild(t),document.head.appendChild(e);},re=()=>{const t=document.createElement("div");return t.style.backgroundImage="linear-gradient(to top, #f9fafb, white)",t.style.border="1px solid #e5e7eb",t.style.borderRadius="0.75rem",t.style.boxShadow="0 0 10px rgba(0, 0, 0, 0.1)",t.style.color="#374151",t.style.display="flex",t.style.flexDirection="row",t.style.alignItems="center",t.style.height="40px",t.style.justifyContent="space-between",t.style.overflow="hidden",t.style.position="fixed",t.style.right=".75rem",t.style.top=".75rem",t.style.width="auto",t.style.zIndex="20",t.style.paddingLeft="1rem",t.setAttribute("id","huggingface-space-header"),window.matchMedia("(max-width: 768px)").addEventListener("change",e=>{e.matches?t.style.display="none":t.style.display="flex";}),t},oe=()=>{const t=document.createElementNS("http://www.w3.org/2000/svg","svg");t.setAttribute("xmlns","http://www.w3.org/2000/svg"),t.setAttribute("xmlns:link","http://www.w3.org/1999/xlink"),t.setAttribute("aria-hidden","true"),t.setAttribute("focusable","false"),t.setAttribute("role","img"),t.setAttribute("width","1em"),t.setAttribute("height","1em"),t.setAttribute("preserveAspectRatio","xMidYMid meet"),t.setAttribute("viewBox","0 0 12 12"),t.setAttribute("fill","currentColor");const e=document.createElementNS("http://www.w3.org/2000/svg","path");return e.setAttribute("d","M0.375001 10.3828L0.375 1.61719C0.375 1.104 0.816001 0.687501 1.35938 0.687501L10.6406 0.6875C10.9017 0.6875 11.1521 0.785449 11.3367 0.959797C11.5213 1.13415 11.625 1.37062 11.625 1.61719V10.3828C11.625 10.6294 11.5213 10.8659 11.3367 11.0402C11.1521 11.2145 10.9017 11.3125 10.6406 11.3125H1.35938C0.816001 11.3125 0.375001 10.896 0.375001 10.3828ZM1.35938 10.5156H10.6406C10.7183 10.5156 10.7813 10.4561 10.7813 10.3828V4.40625H1.21875V10.3828C1.21875 10.418 1.23356 10.4518 1.25994 10.4767C1.28631 10.5017 1.32208 10.5156 1.35938 10.5156ZM4.61052 6.38251L5.9999 7.69472L7.38927 6.38251C7.44083 6.33007 7.50645 6.29173 7.57913 6.27153C7.6518 6.25134 7.72898 6.25003 7.8024 6.26776C7.87583 6.28549 7.9428 6.3216 7.99628 6.37227C8.04983 6.42295 8.08785 6.48631 8.10645 6.5557C8.12528 6.62497 8.12393 6.69773 8.10263 6.76635C8.0814 6.83497 8.0409 6.8969 7.98555 6.94564L6.29802 8.53936C6.21892 8.61399 6.11169 8.65592 5.9999 8.65592C5.8881 8.65592 5.78087 8.61399 5.70177 8.53936L4.01427 6.94564C3.95874 6.89694 3.91814 6.835 3.89676 6.76633C3.87538 6.69766 3.874 6.62483 3.89277 6.55549C3.91154 6.48615 3.94977 6.42287 4.00343 6.37233C4.05708 6.32179 4.12418 6.28585 4.19765 6.2683C4.27098 6.25054 4.34803 6.25178 4.42068 6.27188C4.49334 6.29198 4.55891 6.3302 4.61052 6.38251Z"),t.appendChild(e),t},ne=(t,e)=>{const r=document.createElement("div");return r.setAttribute("id","space-header__collapse"),r.style.display="flex",r.style.flexDirection="row",r.style.alignItems="center",r.style.justifyContent="center",r.style.fontSize="16px",r.style.paddingLeft="10px",r.style.paddingRight="10px",r.style.height="40px",r.style.cursor="pointer",r.style.color="#40546e",r.style.transitionDuration="0.1s",r.style.transitionProperty="all",r.style.transitionTimingFunction="ease-in-out",r.appendChild(oe()),r.addEventListener("click",o=>{o.preventDefault(),o.stopPropagation(),e();}),r.addEventListener("mouseenter",()=>{r.style.color="#213551";}),r.addEventListener("mouseleave",()=>{r.style.color="#40546e";}),r},se=t=>{const e=document.createElement("p");return e.style.margin="0",e.style.padding="0",e.style.color="#9ca3af",e.style.fontSize="14px",e.style.fontFamily="Source Sans Pro, sans-serif",e.style.padding="0px 6px",e.style.borderLeft="1px solid #e5e7eb",e.style.marginLeft="4px",e.textContent=(t??0).toString(),e},ae=()=>{const t=document.createElementNS("http://www.w3.org/2000/svg","svg");t.setAttribute("xmlns","http://www.w3.org/2000/svg"),t.setAttribute("xmlns:link","http://www.w3.org/1999/xlink"),t.setAttribute("aria-hidden","true"),t.setAttribute("focusable","false"),t.setAttribute("role","img"),t.setAttribute("width","1em"),t.setAttribute("height","1em"),t.setAttribute("preserveAspectRatio","xMidYMid meet"),t.setAttribute("viewBox","0 0 32 32"),t.setAttribute("fill","#6b7280");const e=document.createElementNS("http://www.w3.org/2000/svg","path");return e.setAttribute("d","M22.45,6a5.47,5.47,0,0,1,3.91,1.64,5.7,5.7,0,0,1,0,8L16,26.13,5.64,15.64a5.7,5.7,0,0,1,0-8,5.48,5.48,0,0,1,7.82,0L16,10.24l2.53-2.58A5.44,5.44,0,0,1,22.45,6m0-2a7.47,7.47,0,0,0-5.34,2.24L16,7.36,14.89,6.24a7.49,7.49,0,0,0-10.68,0,7.72,7.72,0,0,0,0,10.82L16,29,27.79,17.06a7.72,7.72,0,0,0,0-10.82A7.49,7.49,0,0,0,22.45,4Z"),t.appendChild(e),t},ie=t=>{const e=document.createElement("a");return e.setAttribute("href",`https://huggingface.co/spaces/${t.id}`),e.setAttribute("rel","noopener noreferrer"),e.setAttribute("target","_blank"),e.style.border="1px solid #e5e7eb",e.style.borderRadius="6px",e.style.display="flex",e.style.flexDirection="row",e.style.alignItems="center",e.style.margin="0 0 0 12px",e.style.fontSize="14px",e.style.paddingLeft="4px",e.style.textDecoration="none",e.appendChild(ae()),e.appendChild(se(t.likes)),e},le=(t,e="user")=>{const r=e==="user"?"users":"organizations",o=document.createElement("img");return o.src=`https://huggingface.co/api/${r}/${t}/avatar`,o.style.width="0.875rem",o.style.height="0.875rem",o.style.borderRadius="50%",o.style.flex="none",o.style.marginRight="0.375rem",o},de=t=>{const[e,r]=t.split("/"),o=document.createElement("a");return o.setAttribute("href",`https://huggingface.co/spaces/${t}`),o.setAttribute("rel","noopener noreferrer"),o.setAttribute("target","_blank"),o.style.color="#1f2937",o.style.textDecoration="none",o.style.fontWeight="600",o.style.fontSize="15px",o.style.lineHeight="24px",o.style.flex="none",o.style.fontFamily="IBM Plex Mono, sans-serif",o.addEventListener("mouseover",()=>{o.style.color="#2563eb";}),o.addEventListener("mouseout",()=>{o.style.color="#1f2937";}),o.textContent=r,o},ce=()=>{const t=document.createElement("div");return t.style.marginLeft=".125rem",t.style.marginRight=".125rem",t.style.color="#d1d5db",t.textContent="/",t},ue=t=>{const e=document.createElement("a");return e.setAttribute("href",`https://huggingface.co/${t}`),e.setAttribute("rel","noopener noreferrer"),e.setAttribute("target","_blank"),e.style.color="rgb(107, 114, 128)",e.style.textDecoration="none",e.style.fontWeight="400",e.style.fontSize="16px",e.style.lineHeight="24px",e.style.flex="none",e.style.fontFamily="Source Sans Pro, sans-serif",e.addEventListener("mouseover",()=>{e.style.color="#2563eb";}),e.addEventListener("mouseout",()=>{e.style.color="rgb(107, 114, 128)";}),e.textContent=t,e},pe=t=>{const e=document.createElement("div");return e.style.display="flex",e.style.flexDirection="row",e.style.alignItems="center",e.style.justifyContent="center",e.style.borderRight="1px solid #e5e7eb",e.style.paddingRight="12px",e.style.height="40px",t.type!=="unknown"&&e.appendChild(le(t.author,t.type)),e.appendChild(ue(t.author)),e.appendChild(ce()),e.appendChild(de(t.id)),e.appendChild(ie(t)),e},he=t=>{const e=re(),r=()=>e.style.display="none";return e.appendChild(pe(t)),e.appendChild(ne(t,r)),e},B=async(t,e="user")=>{const r=e==="user"?"users":"organizations";try{return (await fetch(`https://huggingface.co/api/${r}/${t}/avatar`)).ok}catch{return !1}},me=async t=>{try{return await(await fetch(`https://huggingface.co/api/spaces/${t}`)).json()}catch{return null}},fe=(t,e)=>{if(document.body===null)return console.error("document.body is null");document.body.appendChild(t);};async function ge(t,e){if(window===void 0)return console.error("Please run this script in a browser environment");if(Object.values(window.location?.ancestorOrigins??{0:window.document.referrer}).some(p=>new URL(p)?.origin==="https://huggingface.co"))return;te();let o;if(typeof t=="string"){if(o=await me(t),o===null)return console.error("Space not found")}else o=t;const[n,a]=await Promise.all([B(o.author,"user"),B(o.author,"org")]);o.type=n?"user":a?"org":"unknown";const l=he(o);return fe(l),{element:l}}var ye=(t,e)=>ge(t);let ve="complete";const Se=create_ssr_component((t,e,r,o)=>{let n,a$1,l;l=subscribe(b,s=>a$1=s);createEventDispatcher();let {data:i}=e,{autoscroll:c=!1}=e,{version:y="5-37-0"}=e,{initial_height:x}=e,{app_mode:_=!0}=e,{is_embed:u=!1}=e,{theme_mode:D=null}=e,{control_page_title:C=!0}=e,{container:A}=e,T,{space:w}=e,m,R=!1,L=!1;({register:()=>{},subscribe:writable({}).subscribe});let d=i.app;let k=!1;let f;let g;async function U(s,v){if(s&&!v&&window.self===window.top){g&&(g.remove(),g=void 0);const b=await ye(s);b&&(g=b.element);}}onDestroy(()=>{g?.remove();}),e.data===void 0&&r.data&&i!==void 0&&r.data(i),e.autoscroll===void 0&&r.autoscroll&&c!==void 0&&r.autoscroll(c),e.version===void 0&&r.version&&y!==void 0&&r.version(y),e.initial_height===void 0&&r.initial_height&&x!==void 0&&r.initial_height(x),e.app_mode===void 0&&r.app_mode&&_!==void 0&&r.app_mode(_),e.is_embed===void 0&&r.is_embed&&u!==void 0&&r.is_embed(u),e.theme_mode===void 0&&r.theme_mode&&D!==void 0&&r.theme_mode(D),e.control_page_title===void 0&&r.control_page_title&&C!==void 0&&r.control_page_title(C),e.container===void 0&&r.container&&A!==void 0&&r.container(A),e.space===void 0&&r.space&&w!==void 0&&r.space(w);let h,N,W=t.head;do h=!0,t.head=W,n=i.config,n?.app_id&&n.app_id,f&&k&&(f("Error","Deep link was not valid","error"),k=!1),L&&m.dispatchEvent(new CustomEvent("render",{bubbles:!0,cancelable:!1,composed:!0})),d?.config&&a&&U(d?.config?.space_id,u),N=`${t.head+=`<!-- HEAD_svelte-ky6cqz_START --><link rel="stylesheet"${add_attribute("href","./theme.css?v="+n?.theme_hash,0)}>${n?.stylesheets?`${each(n.stylesheets,s=>`${s.startsWith("http:")||s.startsWith("https:")?`<link rel="stylesheet"${add_attribute("href",s,0)}>`:""}`)}`:""}<link rel="manifest" href="/manifest.json"><!-- HEAD_svelte-ky6cqz_END -->`,""} ${validate_component(Fs,"Embed").$$render(t,{display:A&&u,is_embed:u,info:!1,version:y,initial_height:x,space:w,pages:n.pages,current_page:n.current_page,root:n.root,loaded:ve==="complete",fill_width:n?.fill_width||!1,wrapper:m},{wrapper:s=>{m=s,h=!1;}},{default:()=>`${n?.auth_required?`${validate_component(i.Render||missing_component,"svelte:component").$$render(t,{auth_message:n.auth_message,root:n.root,space_id:w,app_mode:_},{},{})}`:`${n&&d?`${validate_component(i.Render||missing_component,"svelte:component").$$render(t,Object.assign({},{app:d},n,{fill_height:!u&&n.fill_height},{theme_mode:T},{control_page_title:C},{target:m},{autoscroll:c},{show_footer:!u},{app_mode:_},{version:y},{search_params:a$1.url.searchParams},{initial_layout:i.layout},{ready:R},{render_complete:L},{add_new_message:f}),{ready:s=>{R=s,h=!1;},render_complete:s=>{L=s,h=!1;},add_new_message:s=>{f=s,h=!1;}},{})}`:""}`}`})}`;while(!h);return l(),N});

export { Se as default };
//# sourceMappingURL=_page.svelte-DeiIev7j.js.map
